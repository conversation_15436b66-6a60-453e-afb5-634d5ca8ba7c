---
import GoogleAnalytics from '@astro-kits/google-analytics';
import { ClientRouter } from "astro:transitions";
import LanguageSwitcher from '../components/LanguageSwitcher.astro';
import {
  getLocaleFromUrl, getLocalizedPath,
  getTprefix,
  Locale,
  useTranslations
} from '../i18n/utils';
import { fileURLToPath } from 'url';
import { relative } from 'path';

// Get the current file path relative to src folder
const currentFilePath = fileURLToPath(import.meta.url);
const srcPath = fileURLToPath(new URL('../../src/', import.meta.url));
const relativeFilePath = relative(srcPath, currentFilePath);
const result = relativeFilePath.split('.').slice(0, -1).join('.').replace(/\//g, '.');
console.log('Result en Layout.astro:', result); // This will log: layouts.Layout

// region translations
const { lang } = Astro.params;
const currentLocale: Locale = (lang as Locale) || 'en';
// Get translation
const t = (key: string) => useTranslations(currentLocale)(getTprefix(import.meta.url) + key);
// endregion
---
<!doctype html>
<html lang={currentLocale}>
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{t('title')}</title>
    <meta name="description" content={t('description')} />
    <ClientRouter />
    <GoogleAnalytics />
	</head>
	<body>
		<LanguageSwitcher />
		<slot />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
