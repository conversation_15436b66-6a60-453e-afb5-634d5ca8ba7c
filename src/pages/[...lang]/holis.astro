---
import Layout from '../../layouts/Layout.astro';
import {
  useTranslations,
  getLocalizedPath,
  type Locale,
  getTprefix
} from "../../i18n/utils";

// region translations
const { lang } = Astro.params;
const currentLocale: Locale = (lang as Locale) || 'en';
// Get translation
const t = (key: string) => useTranslations(currentLocale)(getTprefix(import.meta.url) + key);
// Get path
const p = (path: string) => getLocalizedPath(path, currentLocale);
// endregion

export async function getStaticPaths() {
  return [
    { params: { lang: undefined } }, // English (no prefix) - generates /holis
    { params: { lang: 'es' } }       // Spanish - generates /es/holis
  ];
}
---

<Layout>
  <div style="padding: 2rem; text-align: center;">
    <h1>{t('title')}</h1>
    <a href={p('/')} style="display: inline-block; margin-top: 1rem; padding: 8px 16px; background: #3245ff; color: white; text-decoration: none; border-radius: 6px;">
      {t('go_to_home')}
    </a>
  </div>
</Layout>
